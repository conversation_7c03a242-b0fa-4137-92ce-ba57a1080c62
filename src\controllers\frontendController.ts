import { Request, Response } from 'express';

interface SessionRequest extends Request {
  session: any;
}

export class FrontendController {
  // Authentication pages
  static renderLogin(req: Request, res: Response): void {
    // If user is already logged in, redirect to dashboard
    const sessionReq = req as SessionRequest;
    if (sessionReq.session?.user) {
      res.redirect('/dashboard');
      return;
    }

    res.render('pages/login', {
      title: 'Login',
      user: null,
    });
  }

  static renderRegister(req: Request, res: Response): void {
    // If user is already logged in, redirect to dashboard
    const sessionReq = req as SessionRequest;
    if (sessionReq.session?.user) {
      res.redirect('/dashboard');
      return;
    }

    res.render('pages/register', {
      title: 'Register',
      user: null,
    });
  }

  // Dashboard
  static renderDashboard(req: Request, res: Response): void {
    const sessionReq = req as SessionRequest;

    // For now, let's render the dashboard without authentication requirement
    // In a real app, you'd check authentication here
    res.render('pages/dashboard', {
      title: 'Dashboard',
      user: { fullName: 'Demo User', email: '<EMAIL>' }, // Mock user for demo
    });
  }

  // Transaction pages
  static renderTransactions(req: Request, res: Response): void {
    const { category } = req.params;
    const categoryConfig = getCategoryConfig(category || '');

    if (!categoryConfig) {
      res.status(404).render('pages/404', {
        title: 'Not Found',
        user: { fullName: 'Demo User', email: '<EMAIL>' }
      });
      return;
    }

    res.render('pages/transactions', {
      title: categoryConfig.title,
      user: { fullName: 'Demo User', email: '<EMAIL>' }, // Mock user for demo
      categoryName: category,
      categoryTitle: categoryConfig.title,
      categoryIcon: categoryConfig.icon,
    });
  }

  static renderTransactionForm(req: Request, res: Response): void {
    const { category, id } = req.params;
    const categoryConfig = getCategoryConfig(category || '');

    if (!categoryConfig) {
      res.status(404).render('pages/404', {
        title: 'Not Found',
        user: { fullName: 'Demo User', email: '<EMAIL>' }
      });
      return;
    }

    const isEdit = !!id;
    const title = `${isEdit ? 'Edit' : 'Add'} ${categoryConfig.title.slice(0, -1)}`;

    res.render('pages/transaction-form', {
      title,
      user: { fullName: 'Demo User', email: '<EMAIL>' }, // Mock user for demo
      categoryName: category,
      categoryTitle: categoryConfig.title,
      categoryIcon: categoryConfig.icon,
      isEdit,
      transaction: isEdit ? {
        _id: id,
        amount: 100,
        description: 'Sample transaction',
        date: new Date()
      } : null,
    });
  }

  // Error pages
  static render404(req: Request, res: Response): void {
    res.status(404).render('pages/404', {
      title: 'Page Not Found',
      user: { fullName: 'Demo User', email: '<EMAIL>' },
    });
  }

  static render500(req: Request, res: Response): void {
    res.status(500).render('pages/500', {
      title: 'Server Error',
      user: { fullName: 'Demo User', email: '<EMAIL>' },
    });
  }
}

function getCategoryConfig(category: string) {
  const configs: Record<string, { title: string; icon: string }> = {
    incomes: { title: 'Incomes', icon: 'fas fa-money-bill-wave' },
    needs: { title: 'Needs', icon: 'fas fa-shopping-basket' },
    wants: { title: 'Wants', icon: 'fas fa-gift' },
    investments: { title: 'Investments', icon: 'fas fa-chart-line' },
    donations: { title: 'Donations', icon: 'fas fa-heart' },
  };
  
  return configs[category] || null;
}

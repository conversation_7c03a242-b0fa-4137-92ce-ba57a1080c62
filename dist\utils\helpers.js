"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isValidObjectId = exports.formatPercentage = exports.formatCurrency = exports.calculateTotal = void 0;
const calculateTotal = (records) => {
    return records.reduce((total, record) => total + record.amount, 0);
};
exports.calculateTotal = calculateTotal;
const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
    }).format(amount);
};
exports.formatCurrency = formatCurrency;
const formatPercentage = (value) => {
    return `${value.toFixed(2)}%`;
};
exports.formatPercentage = formatPercentage;
const isValidObjectId = (id) => {
    return /^[0-9a-fA-F]{24}$/.test(id);
};
exports.isValidObjectId = isValidObjectId;
//# sourceMappingURL=helpers.js.map
import { IUser } from '../models';
export interface TokenPayload {
    userId: string;
    deviceId: string;
}
export interface AuthTokens {
    accessToken: string;
    refreshToken: string;
}
export declare class AuthService {
    static hashPassword(password: string): Promise<string>;
    static comparePassword(password: string, hash: string): Promise<boolean>;
    static generateTokens(userId: string, deviceId: string): AuthTokens;
    static verifyAccessToken(token: string): TokenPayload;
    static verifyRefreshToken(token: string): TokenPayload;
    static registerUser(email: string, password: string, fullName: string): Promise<IUser>;
    static loginUser(email: string, password: string, userAgent: string, ip: string): Promise<{
        user: IUser;
        tokens: AuthTokens;
    }>;
    static manageUserDevices(user: IUser, deviceId: string, refreshToken: string, userAgent: string, ip: string): Promise<void>;
    static refreshTokens(refreshToken: string): Promise<AuthTokens>;
    static logoutDevice(userId: string, deviceId: string): Promise<void>;
    static logoutAllDevices(userId: string): Promise<void>;
}
//# sourceMappingURL=authService.d.ts.map
{"name": "@types/connect-flash", "version": "0.0.40", "description": "TypeScript definitions for connect-flash", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/connect-flash", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON>"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Lemmmy"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/connect-flash"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "d51ca2d7e1c3b7972e146e2aba983042872f3bcb9e1913cc341ce35a75483350", "typeScriptVersion": "4.5"}
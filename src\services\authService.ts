import bcrypt from 'bcryptjs';
import jwt, { SignOptions } from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';
import { User, IUser, IDevice } from '../models';
import { config } from '../config/env';

export interface TokenPayload {
  userId: string;
  deviceId: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

export class AuthService {
  static async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, config.BCRYPT_ROUNDS);
  }

  static async comparePassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  static generateTokens(userId: string, deviceId: string): AuthTokens {
    const payload: TokenPayload = { userId, deviceId };

    const accessToken = jwt.sign(payload, config.JWT_SECRET as string, {
      expiresIn: config.JWT_EXPIRES_IN,
    } as SignOptions);

    const refreshToken = jwt.sign(payload, config.REFRESH_TOKEN_SECRET as string, {
      expiresIn: config.REFRESH_TOKEN_EXPIRES_IN,
    } as SignOptions);

    return { accessToken, refreshToken };
  }

  static verifyAccessToken(token: string): TokenPayload {
    return jwt.verify(token, config.JWT_SECRET as string) as TokenPayload;
  }

  static verifyRefreshToken(token: string): TokenPayload {
    return jwt.verify(token, config.REFRESH_TOKEN_SECRET as string) as TokenPayload;
  }

  static async registerUser(email: string, password: string, fullName: string): Promise<IUser> {
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      throw new Error('User already exists');
    }

    const passwordHash = await this.hashPassword(password);
    const user = new User({
      email,
      passwordHash,
      fullName,
      devices: [],
    });

    return user.save();
  }

  static async loginUser(
    email: string,
    password: string,
    userAgent: string,
    ip: string
  ): Promise<{ user: IUser; tokens: AuthTokens }> {
    const user = await User.findOne({ email });
    if (!user) {
      throw new Error('Invalid credentials');
    }

    const isPasswordValid = await this.comparePassword(password, user.passwordHash);
    if (!isPasswordValid) {
      throw new Error('Invalid credentials');
    }

    const deviceId = uuidv4();
    const tokens = this.generateTokens(String(user._id), deviceId);

    // Manage device limit
    await this.manageUserDevices(user, deviceId, tokens.refreshToken, userAgent, ip);

    return { user, tokens };
  }

  static async manageUserDevices(
    user: IUser,
    deviceId: string,
    refreshToken: string,
    userAgent: string,
    ip: string
  ): Promise<void> {
    const newDevice: IDevice = {
      deviceId,
      refreshToken,
      userAgent,
      ip,
      lastSeen: new Date(),
    };

    // If user has reached max devices, remove the oldest one
    if (user.devices.length >= config.MAX_DEVICES_PER_USER) {
      user.devices.sort((a, b) => a.lastSeen.getTime() - b.lastSeen.getTime());
      user.devices.shift(); // Remove oldest device
    }

    user.devices.push(newDevice);
    await user.save();
  }

  static async refreshTokens(refreshToken: string): Promise<AuthTokens> {
    const payload = this.verifyRefreshToken(refreshToken);
    const user = await User.findById(payload.userId);
    
    if (!user) {
      throw new Error('User not found');
    }

    const device = user.devices.find(d => d.deviceId === payload.deviceId);
    if (!device || device.refreshToken !== refreshToken) {
      throw new Error('Invalid refresh token');
    }

    // Generate new tokens
    const newTokens = this.generateTokens(payload.userId, payload.deviceId);
    
    // Update refresh token in database
    device.refreshToken = newTokens.refreshToken;
    device.lastSeen = new Date();
    await user.save();

    return newTokens;
  }

  static async logoutDevice(userId: string, deviceId: string): Promise<void> {
    await User.updateOne(
      { _id: userId },
      { $pull: { devices: { deviceId } } }
    );
  }

  static async logoutAllDevices(userId: string): Promise<void> {
    await User.updateOne(
      { _id: userId },
      { $set: { devices: [] } }
    );
  }
}

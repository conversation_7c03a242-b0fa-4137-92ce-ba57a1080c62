export interface BudgetAllocation {
    needs: number;
    wants: number;
    investments: number;
    donations: number;
}
export interface CategorySummary {
    allocated: number;
    spent: number;
    remaining: number;
    percentUsed: number;
    isOverBudget: boolean;
}
export interface DashboardData {
    totalIncome: number;
    budgetAllocation: BudgetAllocation;
    summary: {
        needs: CategorySummary;
        wants: CategorySummary;
        investments: CategorySummary;
        donations: CategorySummary;
    };
    totalSpent: number;
    totalRemaining: number;
    overallBudgetStatus: 'under' | 'over' | 'exact';
}
export declare class DashboardService {
    private static readonly ALLOCATION_PERCENTAGES;
    static getDashboardData(userId: string, startDate?: Date, endDate?: Date): Promise<DashboardData>;
    private static calculateCategorySummary;
    static getMonthlyTrends(userId: string, months?: number): Promise<any[]>;
}
//# sourceMappingURL=dashboardService.d.ts.map
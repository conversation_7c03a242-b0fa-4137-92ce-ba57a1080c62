"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FrontendController = void 0;
class FrontendController {
    static renderLogin(req, res) {
        const sessionReq = req;
        if (sessionReq.session?.user) {
            res.redirect('/dashboard');
            return;
        }
        res.render('pages/login', {
            title: 'Login',
            user: null,
        });
    }
    static renderRegister(req, res) {
        const sessionReq = req;
        if (sessionReq.session?.user) {
            res.redirect('/dashboard');
            return;
        }
        res.render('pages/register', {
            title: 'Register',
            user: null,
        });
    }
    static renderDashboard(req, res) {
        const sessionReq = req;
        res.render('pages/dashboard', {
            title: 'Dashboard',
            user: { fullName: 'Demo User', email: '<EMAIL>' },
        });
    }
    static renderTransactions(req, res) {
        const { category } = req.params;
        const categoryConfig = getCategoryConfig(category || '');
        if (!categoryConfig) {
            res.status(404).render('pages/404', {
                title: 'Not Found',
                user: { fullName: 'Demo User', email: '<EMAIL>' }
            });
            return;
        }
        res.render('pages/transactions', {
            title: categoryConfig.title,
            user: { fullName: 'Demo User', email: '<EMAIL>' },
            categoryName: category,
            categoryTitle: categoryConfig.title,
            categoryIcon: categoryConfig.icon,
        });
    }
    static renderTransactionForm(req, res) {
        const { category, id } = req.params;
        const categoryConfig = getCategoryConfig(category || '');
        if (!categoryConfig) {
            res.status(404).render('pages/404', {
                title: 'Not Found',
                user: { fullName: 'Demo User', email: '<EMAIL>' }
            });
            return;
        }
        const isEdit = !!id;
        const title = `${isEdit ? 'Edit' : 'Add'} ${categoryConfig.title.slice(0, -1)}`;
        res.render('pages/transaction-form', {
            title,
            user: { fullName: 'Demo User', email: '<EMAIL>' },
            categoryName: category,
            categoryTitle: categoryConfig.title,
            categoryIcon: categoryConfig.icon,
            isEdit,
            transaction: isEdit ? {
                _id: id,
                amount: 100,
                description: 'Sample transaction',
                date: new Date()
            } : null,
        });
    }
    static render404(req, res) {
        res.status(404).render('pages/404', {
            title: 'Page Not Found',
            user: { fullName: 'Demo User', email: '<EMAIL>' },
        });
    }
    static render500(req, res) {
        res.status(500).render('pages/500', {
            title: 'Server Error',
            user: { fullName: 'Demo User', email: '<EMAIL>' },
        });
    }
}
exports.FrontendController = FrontendController;
function getCategoryConfig(category) {
    const configs = {
        incomes: { title: 'Incomes', icon: 'fas fa-money-bill-wave' },
        needs: { title: 'Needs', icon: 'fas fa-shopping-basket' },
        wants: { title: 'Wants', icon: 'fas fa-gift' },
        investments: { title: 'Investments', icon: 'fas fa-chart-line' },
        donations: { title: 'Donations', icon: 'fas fa-heart' },
    };
    return configs[category] || null;
}
//# sourceMappingURL=frontendController.js.map
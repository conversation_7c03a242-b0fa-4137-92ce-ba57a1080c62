import { Document } from 'mongoose';
interface TransactionDocument extends Document {
    amount: number;
}
export declare const calculateTotal: (records: TransactionDocument[]) => number;
export declare const formatCurrency: (amount: number) => string;
export declare const formatPercentage: (value: number) => string;
export declare const isValidObjectId: (id: string) => boolean;
export {};
//# sourceMappingURL=helpers.d.ts.map
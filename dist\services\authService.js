"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const uuid_1 = require("uuid");
const models_1 = require("../models");
const env_1 = require("../config/env");
class AuthService {
    static async hashPassword(password) {
        return bcryptjs_1.default.hash(password, env_1.config.BCRYPT_ROUNDS);
    }
    static async comparePassword(password, hash) {
        return bcryptjs_1.default.compare(password, hash);
    }
    static generateTokens(userId, deviceId) {
        const payload = { userId, deviceId };
        const accessToken = jsonwebtoken_1.default.sign(payload, env_1.config.JWT_SECRET, {
            expiresIn: env_1.config.JWT_EXPIRES_IN,
        });
        const refreshToken = jsonwebtoken_1.default.sign(payload, env_1.config.REFRESH_TOKEN_SECRET, {
            expiresIn: env_1.config.REFRESH_TOKEN_EXPIRES_IN,
        });
        return { accessToken, refreshToken };
    }
    static verifyAccessToken(token) {
        return jsonwebtoken_1.default.verify(token, env_1.config.JWT_SECRET);
    }
    static verifyRefreshToken(token) {
        return jsonwebtoken_1.default.verify(token, env_1.config.REFRESH_TOKEN_SECRET);
    }
    static async registerUser(email, password, fullName) {
        const existingUser = await models_1.User.findOne({ email });
        if (existingUser) {
            throw new Error('User already exists');
        }
        const passwordHash = await this.hashPassword(password);
        const user = new models_1.User({
            email,
            passwordHash,
            fullName,
            devices: [],
        });
        return user.save();
    }
    static async loginUser(email, password, userAgent, ip) {
        const user = await models_1.User.findOne({ email });
        if (!user) {
            throw new Error('Invalid credentials');
        }
        const isPasswordValid = await this.comparePassword(password, user.passwordHash);
        if (!isPasswordValid) {
            throw new Error('Invalid credentials');
        }
        const deviceId = (0, uuid_1.v4)();
        const tokens = this.generateTokens(String(user._id), deviceId);
        await this.manageUserDevices(user, deviceId, tokens.refreshToken, userAgent, ip);
        return { user, tokens };
    }
    static async manageUserDevices(user, deviceId, refreshToken, userAgent, ip) {
        const newDevice = {
            deviceId,
            refreshToken,
            userAgent,
            ip,
            lastSeen: new Date(),
        };
        if (user.devices.length >= env_1.config.MAX_DEVICES_PER_USER) {
            user.devices.sort((a, b) => a.lastSeen.getTime() - b.lastSeen.getTime());
            user.devices.shift();
        }
        user.devices.push(newDevice);
        await user.save();
    }
    static async refreshTokens(refreshToken) {
        const payload = this.verifyRefreshToken(refreshToken);
        const user = await models_1.User.findById(payload.userId);
        if (!user) {
            throw new Error('User not found');
        }
        const device = user.devices.find(d => d.deviceId === payload.deviceId);
        if (!device || device.refreshToken !== refreshToken) {
            throw new Error('Invalid refresh token');
        }
        const newTokens = this.generateTokens(payload.userId, payload.deviceId);
        device.refreshToken = newTokens.refreshToken;
        device.lastSeen = new Date();
        await user.save();
        return newTokens;
    }
    static async logoutDevice(userId, deviceId) {
        await models_1.User.updateOne({ _id: userId }, { $pull: { devices: { deviceId } } });
    }
    static async logoutAllDevices(userId) {
        await models_1.User.updateOne({ _id: userId }, { $set: { devices: [] } });
    }
}
exports.AuthService = AuthService;
//# sourceMappingURL=authService.js.map
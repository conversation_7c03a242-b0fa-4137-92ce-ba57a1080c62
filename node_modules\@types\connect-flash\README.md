# Installation
> `npm install --save @types/connect-flash`

# Summary
This package contains type definitions for connect-flash (https://github.com/jaredhanson/connect-flash).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/connect-flash.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/connect-flash/index.d.ts)
````ts
/// <reference types="express" />

declare namespace Express {
    export interface Request {
        flash(): { [key: string]: string[] };
        flash(message: string): string[];
        flash(type: string, message: string[] | string): number;
        flash(type: string, format: string, ...args: any[]): number;
    }
}

declare module "connect-flash" {
    import express = require("express");
    interface IConnectFlashOptions {
        unsafe?: boolean | undefined;
    }
    function e(options?: IConnectFlashOptions): express.RequestHandler;
    export = e;
}

````

### Additional Details
 * Last updated: Mon, 06 Nov 2023 22:41:05 GMT
 * Dependencies: [@types/express](https://npmjs.com/package/@types/express)

# Credits
These definitions were written by [<PERSON>smann](https://github.com/<PERSON>Gassmann), and [Drew Lemmy](https://github.com/Lemmmy).

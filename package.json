{"name": "dhanfolio-backend", "version": "1.0.0", "description": "Personal finance tracker backend with budget allocation and device management", "main": "dist/server.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "start": "node dist/server.js", "lint": "eslint . --ext .ts", "format": "prettier --write \"src/**/*.ts\"", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["finance", "budget", "tracker", "typescript", "express", "mongodb"], "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "dotenv": "^16.3.1", "zod": "^3.22.4", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "express-mongo-sanitize": "^2.2.0", "uuid": "^9.0.1", "ejs": "^3.1.9", "express-session": "^1.17.3", "connect-flash": "^0.1.1", "express-ejs-layouts": "^2.5.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.10.5", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "typescript": "^5.3.3", "ts-node-dev": "^2.0.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint": "^8.55.0", "prettier": "^3.1.1", "@types/uuid": "^9.0.7", "@types/express-session": "^1.17.10", "@types/express-ejs-layouts": "^2.5.4", "@types/connect-flash": "^0.0.40"}}
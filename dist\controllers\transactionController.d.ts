import { Response } from 'express';
import { Model } from 'mongoose';
import { AuthenticatedRequest } from '../middlewares/auth';
export declare class TransactionController {
    private model;
    private categoryName;
    constructor(model: Model<any>, categoryName: string);
    create: (req: AuthenticatedRequest, res: Response) => Promise<void>;
    getAll: (req: AuthenticatedRequest, res: Response) => Promise<void>;
    getById: (req: AuthenticatedRequest, res: Response) => Promise<void>;
    update: (req: AuthenticatedRequest, res: Response) => Promise<void>;
    delete: (req: AuthenticatedRequest, res: Response) => Promise<void>;
}
//# sourceMappingURL=transactionController.d.ts.map
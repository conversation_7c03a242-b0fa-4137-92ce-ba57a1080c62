"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DashboardService = void 0;
const models_1 = require("../models");
const helpers_1 = require("../utils/helpers");
class DashboardService {
    static async getDashboardData(userId, startDate, endDate) {
        const dateFilter = { userId };
        if (startDate || endDate) {
            dateFilter.date = {};
            if (startDate)
                dateFilter.date.$gte = startDate;
            if (endDate)
                dateFilter.date.$lte = endDate;
        }
        const [incomes, needs, wants, investments, donations] = await Promise.all([
            models_1.Income.find(dateFilter),
            models_1.Need.find(dateFilter),
            models_1.Want.find(dateFilter),
            models_1.Investment.find(dateFilter),
            models_1.Donation.find(dateFilter),
        ]);
        const totalIncome = (0, helpers_1.calculateTotal)(incomes);
        const totalNeeds = (0, helpers_1.calculateTotal)(needs);
        const totalWants = (0, helpers_1.calculateTotal)(wants);
        const totalInvestments = (0, helpers_1.calculateTotal)(investments);
        const totalDonations = (0, helpers_1.calculateTotal)(donations);
        const budgetAllocation = {
            needs: totalIncome * this.ALLOCATION_PERCENTAGES.needs,
            wants: totalIncome * this.ALLOCATION_PERCENTAGES.wants,
            investments: totalIncome * this.ALLOCATION_PERCENTAGES.investments,
            donations: totalIncome * this.ALLOCATION_PERCENTAGES.donations,
        };
        const summary = {
            needs: this.calculateCategorySummary(budgetAllocation.needs, totalNeeds),
            wants: this.calculateCategorySummary(budgetAllocation.wants, totalWants),
            investments: this.calculateCategorySummary(budgetAllocation.investments, totalInvestments),
            donations: this.calculateCategorySummary(budgetAllocation.donations, totalDonations),
        };
        const totalSpent = totalNeeds + totalWants + totalInvestments + totalDonations;
        const totalAllocated = Object.values(budgetAllocation).reduce((sum, amount) => sum + amount, 0);
        const totalRemaining = totalAllocated - totalSpent;
        let overallBudgetStatus = 'exact';
        if (totalSpent > totalAllocated) {
            overallBudgetStatus = 'over';
        }
        else if (totalSpent < totalAllocated) {
            overallBudgetStatus = 'under';
        }
        return {
            totalIncome,
            budgetAllocation,
            summary,
            totalSpent,
            totalRemaining,
            overallBudgetStatus,
        };
    }
    static calculateCategorySummary(allocated, spent) {
        const remaining = allocated - spent;
        const percentUsed = allocated > 0 ? (spent / allocated) * 100 : 0;
        const isOverBudget = spent > allocated;
        return {
            allocated: Math.round(allocated * 100) / 100,
            spent: Math.round(spent * 100) / 100,
            remaining: Math.round(remaining * 100) / 100,
            percentUsed: Math.round(percentUsed * 100) / 100,
            isOverBudget,
        };
    }
    static async getMonthlyTrends(userId, months = 6) {
        const mockData = [];
        const currentDate = new Date();
        for (let i = months - 1; i >= 0; i--) {
            const date = new Date(currentDate);
            date.setMonth(date.getMonth() - i);
            mockData.push({
                year: date.getFullYear(),
                month: date.getMonth() + 1,
                income: Math.floor(Math.random() * 1000) + 4000,
                needs: Math.floor(Math.random() * 500) + 800,
                wants: Math.floor(Math.random() * 400) + 600,
                investments: Math.floor(Math.random() * 600) + 1500,
                donations: Math.floor(Math.random() * 100) + 200,
            });
        }
        return mockData;
    }
}
exports.DashboardService = DashboardService;
DashboardService.ALLOCATION_PERCENTAGES = {
    needs: 0.25,
    wants: 0.25,
    investments: 0.45,
    donations: 0.05,
};
//# sourceMappingURL=dashboardService.js.map
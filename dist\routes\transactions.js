"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createTransactionRoutes = void 0;
const express_1 = require("express");
const validation_1 = require("../middlewares/validation");
const auth_1 = require("../middlewares/auth");
const validation_2 = require("../utils/validation");
const createTransactionRoutes = (controller) => {
    const router = (0, express_1.Router)();
    router.use(auth_1.authenticate);
    router.post('/', (0, validation_1.validate)(validation_2.transactionSchema), controller.create);
    router.get('/', (0, validation_1.validateQuery)(validation_2.querySchema), controller.getAll);
    router.get('/:id', controller.getById);
    router.put('/:id', (0, validation_1.validate)(validation_2.updateTransactionSchema), controller.update);
    router.delete('/:id', controller.delete);
    return router;
};
exports.createTransactionRoutes = createTransactionRoutes;
//# sourceMappingURL=transactions.js.map
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const path_1 = __importDefault(require("path"));
const express_session_1 = __importDefault(require("express-session"));
const connect_flash_1 = __importDefault(require("connect-flash"));
const express_ejs_layouts_1 = __importDefault(require("express-ejs-layouts"));
const env_1 = require("./config/env");
const middlewares_1 = require("./middlewares");
const routes_1 = __importDefault(require("./routes"));
const frontend_1 = __importDefault(require("./routes/frontend"));
const app = (0, express_1.default)();
app.set('trust proxy', 1);
app.use(express_ejs_layouts_1.default);
app.set('view engine', 'ejs');
app.set('views', path_1.default.join(__dirname, '../views'));
app.set('layout', 'layouts/main');
app.use(express_1.default.static(path_1.default.join(__dirname, '../public')));
app.use((0, express_session_1.default)({
    secret: env_1.config.JWT_SECRET,
    resave: false,
    saveUninitialized: false,
    cookie: {
        secure: env_1.config.NODE_ENV === 'production',
        maxAge: 24 * 60 * 60 * 1000,
    },
}));
app.use((0, connect_flash_1.default)());
app.use((req, res, next) => {
    res.locals.messages = req.flash ? req.flash() : {};
    next();
});
app.use((0, middlewares_1.setupSecurity)());
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
app.get('/health', (req, res) => {
    res.status(200).json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        environment: env_1.config.NODE_ENV,
    });
});
app.use('/api/auth', middlewares_1.authRateLimit);
app.use('/api', routes_1.default);
app.use('/', frontend_1.default);
app.use(middlewares_1.notFound);
app.use(middlewares_1.errorHandler);
exports.default = app;
//# sourceMappingURL=app.js.map
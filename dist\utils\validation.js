"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.querySchema = exports.updateTransactionSchema = exports.transactionSchema = exports.refreshTokenSchema = exports.loginSchema = exports.registerSchema = void 0;
const zod_1 = require("zod");
exports.registerSchema = zod_1.z.object({
    email: zod_1.z.string().email('Invalid email format'),
    password: zod_1.z.string().min(6, 'Password must be at least 6 characters'),
    fullName: zod_1.z.string().min(1, 'Full name is required').trim(),
});
exports.loginSchema = zod_1.z.object({
    email: zod_1.z.string().email('Invalid email format'),
    password: zod_1.z.string().min(1, 'Password is required'),
});
exports.refreshTokenSchema = zod_1.z.object({
    refreshToken: zod_1.z.string().min(1, 'Refresh token is required'),
});
exports.transactionSchema = zod_1.z.object({
    amount: zod_1.z.number().positive('Amount must be positive'),
    description: zod_1.z.string().min(1, 'Description is required').trim(),
    date: zod_1.z.string().datetime().optional(),
});
exports.updateTransactionSchema = zod_1.z.object({
    amount: zod_1.z.number().positive('Amount must be positive').optional(),
    description: zod_1.z.string().min(1, 'Description is required').trim().optional(),
    date: zod_1.z.string().datetime().optional(),
});
exports.querySchema = zod_1.z.object({
    page: zod_1.z.string().regex(/^\d+$/).transform(Number).optional(),
    limit: zod_1.z.string().regex(/^\d+$/).transform(Number).optional(),
    startDate: zod_1.z.string().datetime().optional(),
    endDate: zod_1.z.string().datetime().optional(),
});
//# sourceMappingURL=validation.js.map